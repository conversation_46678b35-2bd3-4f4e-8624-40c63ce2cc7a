import { Routes, Route, useLocation, Navigate } from "react-router-dom";
import { AnimatePresence } from "framer-motion";
import { lazy, useEffect } from "react";
import { handlePageChangeScroll } from "./utils/scrollHelper";



const Home = lazy(() => import("./pages/Home"));
const ReaderSelection = lazy(() => import("./pages/ReaderSelection"));
const History = lazy(() => import("./pages/History"));
const HistoryDetail = lazy(() => import("./pages/HistoryDetail"));
const Membership = lazy(() => import("./pages/Membership"));
const MembershipInter = lazy(() => import("./pages/Membership_inter"));
const Login = lazy(() => import("./pages/Login"));
const Register = lazy(() => import("./pages/Register"));
const VerifyEmail = lazy(() => import("./pages/VerifyEmail"));
const SpreadSelection = lazy(() => import("./pages/SpreadSelection"));
const ForgotPassword = lazy(() => import("./pages/ForgotPassword"));
const ResetPassword = lazy(() => import("./pages/ResetPassword"));
const CardBackSettings = lazy(() => import("./pages/CardBackSettings"));
const Spreads = lazy(() => import("./pages/Spreads"));
const SpreadDetail = lazy(() => import("./pages/SpreadDetail"));
const PrivacyPolicy = lazy(() => import("./pages/PrivacyPolicy"));
const TermsOfService = lazy(() => import("./pages/TermsOfService"));
const PaymentSuccess = lazy(() => import("./pages/PaymentSuccess"));
const PaymentError = lazy(() => import("./pages/PaymentError"));
const Precautions = lazy(() => import("./pages/Precautions"));
const VoiceChat = lazy(() => import("./pages/VoiceChat"));
const TarotCardSelection = lazy(
  () => import("./components/TarotCardSelection")
);
const TarotResultPage = lazy(() => import("./pages/TarotResultPage"));
const UserCard = lazy(() => import("./pages/UserCard"));
const DailyFortune = lazy(() => import("./pages/DailyFortune"));
const DailyFortuneResult = lazy(() => import("./pages/DailyFortuneResult"));
const YearlyFortune = lazy(() => import("./pages/YearlyFortune"));
const YearlyFortuneResult = lazy(() => import("./pages/YearlyFortuneResult"));
const NewPage = lazy(() => import("./pages/NewPage"));
const LanguageWrapper = lazy(() => import("./components/LanguageWrapper"));
const InvitationCodeManagement = lazy(
  () => import("./pages/InvitationCodeManagement")
);
const Feedback = lazy(() => import("./pages/Feedback"));
const TarotGallery = lazy(() => import("./pages/TarotGallery"));
const TarotCardDetail = lazy(() => import("./pages/TarotCardDetail"));
const Blog = lazy(() => import("./pages/Blog"));
const BlogDetail = lazy(() => import("./pages/BlogDetail"));
const TtsGeneratorPage = lazy(() => import("./pages/TtsGeneratorPage"));
const YesNoTarot = lazy(() => import("./pages/YesNoTarot"));
const YesNoSingleCard = lazy(() => import("./pages/YesNoSingleCard"));
const YesNoThreeCard = lazy(() => import("./pages/YesNoThreeCard"));
// 导入星座运势相关组件
const Horoscope = lazy(() => import("./pages/horoscope/Horoscope"));
const HoroscopeSelection = lazy(() => import("./pages/horoscope/HoroscopeSelection"));
const DailyHoroscope = lazy(() => import("./pages/horoscope/DailyHoroscope"));
const WeeklyHoroscope = lazy(() => import("./pages/horoscope/WeeklyHoroscope"));
const YearlyHoroscope = lazy(() => import("./pages/horoscope/YearlyHoroscope"));
const HoroscopeDetail = lazy(() => import("./pages/horoscope/HoroscopeDetail"));
const MonthlyHoroscope = lazy(() => import("./pages/horoscope/MonthlyHoroscope"));
const LoveCompatibility = lazy(() => import("./pages/horoscope/LoveCompatibility"));
const UnifiedHoroscopeDetail = lazy(() => import("./pages/horoscope/UnifiedHoroscopeDetail"));
// 导入博客子页面组件
const TarotGuide = lazy(() => import("./pages/TarotGuide"));
const ZodiacTraits = lazy(() => import("./pages/ZodiacTraits"));
const GeneralDivination = lazy(() => import("./pages/GeneralDivination"));

const AppRoutes = () => {
  const location = useLocation();

  // 添加路由变化时的滚动处理
  useEffect(() => {
    // 使用我们的辅助函数处理滚动
    handlePageChangeScroll();
  }, [location.pathname]);

  return (
    <main className="pt-16">
      <AnimatePresence mode="sync" key={location.pathname}>
        <Routes location={location}>
            {/* 根路径直接显示NewPage，不再重定向 */}
            <Route path="/" element={<NewPage />} />

            {/* 所有根路径不再重定向，直接使用对应组件 */}
            <Route path="/home" element={<Home />} />

            {/* 处理简化语言路径 /zh -> /zh-CN */}
            <Route path="/zh" element={<Navigate to="/zh-CN" replace />} />
            <Route
              path="/zh/*"
              element={
                <Navigate
                  to={`/zh-CN/${location.pathname.replace(/^\/zh\//, "")}`}
                  replace
                />
              }
            />

            {/* 语言包装器 - 所有带语言参数的路由 */}
            <Route path="/:lang" element={<LanguageWrapper />}>
              {/* 将首页路由添加到语言包装器中，可以通过/:lang访问 */}
              <Route index element={<NewPage />} />
              <Route path="home" element={<Home />} />
              {/* 添加占卜师选择页面到语言路由系统 */}
              <Route path="reading/reader" element={<ReaderSelection />} /> 
              <Route path="reading/spread" element={<SpreadSelection />} />
              <Route path="reading/shuffle" element={<TarotCardSelection />} />
              <Route path="tarot-result" element={<TarotResultPage />} />
              {/* 添加TTS生成器页面到语言路由系统 */}
              <Route path="tts-generator" element={<TtsGeneratorPage />} />
              {/* 添加语音聊天页面到语言路由系统 */}
              <Route path="voice-chat" element={<VoiceChat />} />
              {/* 添加年度运势页面到语言路由系统 */}
              <Route path="yearly-fortune" element={<YearlyFortune />} />
              <Route
                path="yearly-fortune-result"
                element={<YearlyFortuneResult />}
              />
              {/* 添加日运页面到语言路由系统 */}
              <Route path="daily-fortune" element={<DailyFortune />} />
              <Route
                path="daily-fortune-result"
                element={<DailyFortuneResult />}
              />
              {/* 添加Yes/No塔罗页面到语言路由系统 */}
              <Route path="yes-no-tarot" element={<YesNoTarot />} />
              <Route path="yes-no-tarot/single-card" element={<YesNoSingleCard />} />
              <Route path="yes-no-tarot/three-cards" element={<YesNoThreeCard />} />
              {/* 添加牌阵介绍页面到语言路由系统 */}
              <Route path="spreads" element={<Spreads />} />
              <Route path="spreads/:spreadId" element={<SpreadDetail />} />
              {/* 添加会员订阅页面到语言路由系统 */}
              <Route path="membership" element={<Membership />} />
              {/* 添加内部会员订阅页面到语言路由系统 */}
              <Route path="membership-inter" element={<MembershipInter />} />
              {/* 添加塔罗牌图鉴页面到语言路由系统 */}
              <Route path="gallery" element={<TarotGallery />} />
              {/* 添加卡牌详情页面到语言路由系统 */}
              <Route
                path="gallery/card/:cardName"
                element={<TarotCardDetail />}
              />
              {/* 添加个人中心页面到语言路由系统 */}
              <Route path="user-card" element={<UserCard />} />
              {/* 添加卡背设置页面到语言路由系统 */}
              <Route path="card-back-settings" element={<CardBackSettings />} />
              {/* 添加登录页面到语言路由系统 */}
              <Route path="login" element={<Login />} />
              {/* 添加注册页面到语言路由系统 */}
              <Route path="register" element={<Register />} />
              {/* 添加忘记密码页面到语言路由系统 */}
              <Route path="forgot-password" element={<ForgotPassword />} />
              {/* 添加重置密码页面到语言路由系统 */}
              <Route path="reset-password" element={<ResetPassword />} />
              {/* 添加邮箱验证页面到语言路由系统 */}
              <Route path="verify-email" element={<VerifyEmail />} />
              {/* 添加销售人员邀请码管理页面到语言路由系统 */}
              <Route
                path="sales/invitation-management"
                element={<InvitationCodeManagement />}
              />
              {/* 添加反馈页面到语言路由系统 */}
              <Route path="feedback" element={<Feedback />} />
              {/* 添加隐私政策页面到语言路由系统 */}
              <Route path="privacy" element={<PrivacyPolicy />} />
              {/* 添加服务条款页面到语言路由系统 */}
              <Route path="terms" element={<TermsOfService />} />
              {/* 添加博客页面到语言路由系统 */}
              <Route path="blog" element={<Blog />} />
              {/* 添加博客子页面到语言路由系统 - 保留原有路径以确保兼容性 */}
              <Route path="blog/tarot-guide" element={<TarotGuide />} />
              <Route path="blog/zodiac-traits" element={<ZodiacTraits />} />
              <Route path="blog/general-divination" element={<GeneralDivination />} />
              {/* 添加不带blog前缀的路径 */}
              <Route path="tarot-guide" element={<TarotGuide />} />
              <Route path="zodiac-traits" element={<ZodiacTraits />} />
              <Route path="general-divination" element={<GeneralDivination />} />
              {/* 添加博客详情页到语言路由系统 */}
              <Route path="blog/:slug" element={<BlogDetail />} />
              {/* 添加新的基于类别的博客详情页路由 */}
              <Route path="zodiac-traits/:slug" element={<BlogDetail />} />
              <Route path="tarot-guide/:slug" element={<BlogDetail />} />
              <Route path="general-divination/:slug" element={<BlogDetail />} />
              {/* 添加星座运势页面到语言路由系统 */}
              <Route path="horoscope" element={<Horoscope />} />
              <Route path="horoscope/selection" element={<HoroscopeSelection />} />
              <Route path="horoscope/daily-horoscope" element={<DailyHoroscope />} />
              <Route path="horoscope/weekly-horoscope" element={<WeeklyHoroscope />} />
              <Route path="horoscope/monthly-horoscope" element={<MonthlyHoroscope />} />
              <Route path="horoscope/yearly-horoscope" element={<YearlyHoroscope />} />
              <Route path="horoscope/love-horoscope" element={<LoveCompatibility />} />
              <Route path="horoscope/:signId/daily/:day" element={<DailyHoroscope />} />
              <Route path="horoscope/:signId/weekly" element={<WeeklyHoroscope />} />
              {/* 统一的星座详情页面路由 */}
              <Route path="horoscope/:horoscopeParam" element={<UnifiedHoroscopeDetail />} />
              {/* 添加星座运势详情页到语言路由系统 */}
              <Route path="blog/horoscope/:signId/:periodType" element={<HoroscopeDetail />} />
              {/* 添加历史记录页面到语言路由系统 */}
              <Route path="history" element={<History />} />
              <Route path="history/:id" element={<HistoryDetail />} />
              {/* 可以逐步添加其他页面到语言包装器中 */}
            </Route>

            {/* 所有根路径直接使用对应组件，不再重定向 */}
            <Route path="/reading/reader" element={<ReaderSelection />} />
            <Route path="/reading/spread" element={<SpreadSelection />} />
            <Route path="/reading/shuffle" element={<TarotCardSelection />} />
            <Route path="/tarot-result" element={<TarotResultPage />} />
            <Route path="/yearly-fortune" element={<YearlyFortune />} />
            <Route path="/yearly-fortune-result" element={<YearlyFortuneResult />} />
            <Route path="/daily-fortune" element={<DailyFortune />} />
            <Route path="/daily-fortune-result" element={<DailyFortuneResult />} />
            <Route path="/voice-chat" element={<VoiceChat />} />
            <Route path="/yes-no-tarot" element={<YesNoTarot />} />
            <Route path="/yes-no-tarot/single-card" element={<YesNoSingleCard />} />
            <Route path="/yes-no-tarot/three-cards" element={<YesNoThreeCard />} />
            {/* 添加星座运势相关路由 */}
            <Route path="/horoscope" element={<Horoscope />} />
            <Route path="/horoscope/selection" element={<HoroscopeSelection />} />
            <Route path="/horoscope/daily-horoscope" element={<DailyHoroscope />} />
            <Route path="/horoscope/weekly-horoscope" element={<WeeklyHoroscope />} />
            <Route path="/horoscope/monthly-horoscope" element={<MonthlyHoroscope />} />
            <Route path="/horoscope/yearly-horoscope" element={<YearlyHoroscope />} />
            <Route path="/horoscope/love-horoscope" element={<LoveCompatibility />} />
            {/* 统一的星座详情页面路由 */}
            <Route path="/horoscope/:horoscopeParam" element={<UnifiedHoroscopeDetail />} />
            <Route path="/login" element={<Login />} />
            <Route path="/spreads" element={<Spreads />} />
            <Route path="/spreads/:spreadId" element={<SpreadDetail />} />
            <Route path="/membership" element={<Membership />} />
            <Route path="/membership-inter" element={<MembershipInter />} />
            <Route path="/gallery" element={<TarotGallery />} />
            <Route path="/gallery/card/:cardName" element={<TarotCardDetail />} />
            <Route path="/tts-generator" element={<TtsGeneratorPage />} />
            <Route path="/user-card" element={<UserCard />} />
            <Route path="/card-back-settings" element={<CardBackSettings />} />
            <Route path="/register" element={<Register />} />
            <Route path="/forgot-password" element={<ForgotPassword />} />
            <Route path="/reset-password" element={<ResetPassword />} />
            <Route path="/verify-email" element={<VerifyEmail />} />
            <Route path="/sales/invitation-management" element={<InvitationCodeManagement />} />
            <Route path="/feedback" element={<Feedback />} />
            <Route path="/privacy" element={<PrivacyPolicy />} />
            <Route path="/terms" element={<TermsOfService />} />
            <Route path="/blog" element={<Blog />} />
            {/* 添加博客子页面路由 */}
            <Route path="/blog/tarot-guide" element={<TarotGuide />} />
            <Route path="/blog/zodiac-traits" element={<ZodiacTraits />} />
            <Route path="/blog/general-divination" element={<GeneralDivination />} />
            {/* 添加不带blog前缀的路径 */}
            <Route path="/tarot-guide" element={<TarotGuide />} />
            <Route path="/zodiac-traits" element={<ZodiacTraits />} />
            <Route path="/general-divination" element={<GeneralDivination />} />
            <Route path="/blog/:slug" element={<BlogDetail />} />
            {/* 添加新的基于类别的博客详情页路由 */}
            <Route path="/zodiac-traits/:slug" element={<BlogDetail />} />
            <Route path="/tarot-guide/:slug" element={<BlogDetail />} />
            <Route path="/general-divination/:slug" element={<BlogDetail />} />
            <Route path="/blog/horoscope/:signId/:periodType" element={<HoroscopeDetail />} />
            <Route path="/history" element={<History />} />
            <Route path="/history/:id" element={<HistoryDetail />} />
            <Route path="/payment/success" element={<PaymentSuccess />} />
            <Route path="/payment/error" element={<PaymentError />} />
            <Route path="/precautions" element={<Precautions />} />
        </Routes>
      </AnimatePresence>
    </main>
  );
};

export default AppRoutes;
