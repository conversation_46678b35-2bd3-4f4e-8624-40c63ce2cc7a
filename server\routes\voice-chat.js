const express = require('express');
const router = express.Router();
const { asyncHandler } = require('../utils/asyncHandler');
const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');
const WebSocket = require('ws');
const { v4: uuidv4 } = require('uuid');

// 存储活跃的语音对话会话
const activeSessions = new Map();

// 连接到语音API
router.post('/connect', asyncHandler(async (req, res) => {
  const sessionId = uuidv4();
  console.log(`创建新的语音对话会话: ${sessionId}`);
  
  try {
    // 创建一个WebSocket服务器用于与前端通信
    const wss = new WebSocket.Server({ noServer: true });
    console.log(`为会话 ${sessionId} 创建WebSocket服务器`);
    
    // 存储会话信息
    activeSessions.set(sessionId, {
      id: sessionId,
      wss,
      clients: new Set(),
      pythonProcess: null,
      active: true,
      isRecording: false,  // 添加录音状态标志
      createdAt: new Date()
    });
    console.log(`会话 ${sessionId} 已添加到活跃会话列表，当前活跃会话数: ${activeSessions.size}`);
    
    // 返回会话ID给前端
    res.status(200).json({ 
      success: true, 
      sessionId,
      message: '语音对话会话已创建' 
    });
  } catch (error) {
    console.log('创建语音对话会话失败:', error);
    res.status(500).json({ 
      success: false, 
      message: '创建语音对话会话失败' 
    });
  }
}));

// 启动Python实时对话进程
router.post('/start/:sessionId', asyncHandler(async (req, res) => {
  const { sessionId } = req.params;
  console.log(`请求启动会话 ${sessionId} 的Python进程`);
  
  const session = activeSessions.get(sessionId);
  
  if (!session) {
    console.log(`会话 ${sessionId} 不存在，无法启动Python进程`);
    return res.status(404).json({
      success: false,
      message: '会话不存在'
    });
  }
  
  try {
    // 启动Python进程
    const pythonScriptPath = path.join(__dirname, '..', '..', 'realtime_dialog', 'realtime_dialog', 'main.py');
    console.log(`Python脚本路径: ${pythonScriptPath}`);
    
    // 确保Python脚本存在
    if (!fs.existsSync(pythonScriptPath)) {
      console.error(`Python脚本不存在: ${pythonScriptPath}`);
      return res.status(500).json({
        success: false,
        message: 'Python脚本不存在'
      });
    }
    
    console.log(`启动Python进程: python ${pythonScriptPath}`);
    // 启动Python进程
    const pythonProcess = spawn('python', [pythonScriptPath], {
      stdio: ['pipe', 'pipe', 'pipe']
    });
    
    // 存储Python进程
    session.pythonProcess = pythonProcess;
    console.log(`Python进程已启动，PID: ${pythonProcess.pid}`);
    
    // 处理Python进程的输出
    pythonProcess.stdout.on('data', (data) => {
      const output = data.toString();
      console.log(`Python输出: ${output}`);
      
      // 将输出广播给所有连接的WebSocket客户端
      session.clients.forEach(client => {
        if (client.readyState === WebSocket.OPEN) {
          client.send(JSON.stringify({
            type: 'output',
            data: output
          }));
        }
      });
    });
    
    // 处理Python进程的错误
    pythonProcess.stderr.on('data', (data) => {
      const error = data.toString();
      console.error(`Python错误: ${error}`);
      
      // 将错误广播给所有连接的WebSocket客户端
      session.clients.forEach(client => {
        if (client.readyState === WebSocket.OPEN) {
          client.send(JSON.stringify({
            type: 'error',
            data: error
          }));
        }
      });
    });
    
    // 处理Python进程退出
    pythonProcess.on('close', (code) => {
      console.log(`Python进程退出，退出码: ${code}`);
      
      // 通知所有客户端进程已退出
      session.clients.forEach(client => {
        if (client.readyState === WebSocket.OPEN) {
          client.send(JSON.stringify({
            type: 'exit',
            data: { code }
          }));
        }
      });
      
      // 清理会话
      session.active = false;
      session.pythonProcess = null;
    });
    
    res.status(200).json({
      success: true,
      message: '语音对话进程已启动'
    });
  } catch (error) {
    console.error('启动Python进程失败:', error);
    res.status(500).json({
      success: false,
      message: '启动Python进程失败'
    });
  }
}));

// 断开语音对话会话
router.post('/disconnect/:sessionId', asyncHandler(async (req, res) => {
  const { sessionId } = req.params;
  const session = activeSessions.get(sessionId);
  
  if (!session) {
    return res.status(404).json({
      success: false,
      message: '会话不存在'
    });
  }
  
  try {
    // 终止Python进程
    if (session.pythonProcess) {
      session.pythonProcess.kill();
    }
    
    // 关闭所有WebSocket连接
    session.clients.forEach(client => {
      if (client.readyState === WebSocket.OPEN) {
        client.close();
      }
    });
    
    // 从活跃会话中移除
    activeSessions.delete(sessionId);
    
    res.status(200).json({
      success: true,
      message: '语音对话会话已断开'
    });
  } catch (error) {
    console.error('断开语音对话会话失败:', error);
    res.status(500).json({
      success: false,
      message: '断开语音对话会话失败'
    });
  }
}));

// 处理WebSocket连接的中间件
const handleWebSocketConnection = (server) => {
  // 处理WebSocket升级请求
  server.on('upgrade', (request, socket, head) => {
    const pathname = new URL(request.url, `http://${request.headers.host}`).pathname;
    console.log(`收到WebSocket升级请求: ${pathname}`);
    
    // 检查是否是语音对话的WebSocket请求
    if (pathname.startsWith('/api/voice-chat/ws/')) {
      const sessionId = pathname.split('/').pop();
      console.log(`WebSocket连接请求的会话ID: ${sessionId}`);
      
      const session = activeSessions.get(sessionId);
      
      if (!session) {
        console.log(`会话 ${sessionId} 不存在，拒绝WebSocket连接`);
        socket.destroy();
        return;
      }
      
      console.log(`处理会话 ${sessionId} 的WebSocket升级请求`);
      session.wss.handleUpgrade(request, socket, head, (ws) => {
        session.wss.emit('connection', ws, request);
        console.log(`会话 ${sessionId} 的WebSocket连接已建立`);
        
        // 添加到会话的客户端列表
        session.clients.add(ws);
        
        // 发送欢迎消息
        ws.send(JSON.stringify({
          type: 'connected',
          data: { sessionId: session.id }
        }));
        
        // 处理来自客户端的消息
        ws.on('message', (message) => {
          try {
            const parsedMessage = JSON.parse(message.toString());
            console.log(`收到客户端消息类型: ${parsedMessage.type}`);
            
            // 处理不同类型的消息
            if (parsedMessage.type === 'start_recording') {
              console.log(`收到开始录音信号 - 设备: ${parsedMessage.deviceLabel || parsedMessage.deviceId}`);
              session.isRecording = true;  // 设置会话录音状态
            } else if (parsedMessage.type === 'stop_recording') {
              console.log('收到停止录音信号');
              session.isRecording = false;  // 清除会话录音状态
            } else if (parsedMessage.type === 'audio' && session.pythonProcess && session.isRecording) {
              // 只有在录音状态下才发送音频数据给Python进程
              const audioData = Buffer.from(parsedMessage.data, 'base64');
              session.pythonProcess.stdin.write(audioData);
            }
          } catch (error) {
            console.error('处理WebSocket消息失败:', error);
          }
        });
        
        // 处理连接关闭
        ws.on('close', () => {
          console.log(`会话 ${sessionId} 的WebSocket连接已关闭`);
          session.clients.delete(ws);
        });
      });
    }
  });
};

module.exports = {
  router,
  handleWebSocketConnection
}; 