# Tarot 项目部署指南

<!-- ffmpeg -i "public\images\v3.mp4" -c:v libvpx-vp9 -crf 30 -b:v 0 -c:a libopus -deadline good -cpu-used 8 -y "public\images\v3.webm" -->

# 正常部署（会构建新版本并保存） 
.\scripts\deploy\deploy.ps1 -version "v2.6.33" -server_ip "**************" -ssh_key_path "C:\Users\<USER>\.ssh\tarot.pem"

# 回档到本地指定版本
.\scripts\deploy\deploy.ps1 -version "v2.6.12" -server_ip "**************" -ssh_key_path "C:\Users\<USER>\.ssh\tarot.pem" -use_local_version

# 服务器上查看部署状态
./scripts/deploy/status.sh

> ~/.pm2/logs/tarot-backend-error-0.log
sudo sh -c 'echo "" > /root/.pm2/logs/tarot-backend-error-0.log'
sudo sh -c 'echo "" > /root/.pm2/logs/tarot-backend-out-0.log'
/root/.pm2/logs/

sudo apt update && sudo apt upgrade -y
<!-- Fail2ban 来防止暴力破解 -->
sudo apt install fail2ban -y
<!-- 配置防火墙（UFW） -->
sudo apt install ufw
sudo ufw default deny incoming
sudo ufw default allow outgoing
sudo ufw allow ssh
sudo ufw enable

## 1. 环境准备

首先在服务器上安装必要的软件：

```bash
# 更新包管理器  `
apt update
apt upgrade -y
apt install unzip

# 安装 Node.js 和 npm
curl -fsSL https://deb.nodesource.com/setup_18.x | bash -
apt install -y nodejs

npm config set registry https://registry.npmmirror.com
# 安装 pnpm
npm install -g pnpm

# 安装 nginx
apt install -y nginx

# 安装 pm2 用于进程管理
npm install -g pm2
```


mkdir -p /var/www/tarot
mkdir -p /var/www/tarot/scripts/deploy
mkdir -p /var/www/tarot/versions
chmod +x /var/www/tarot/scripts/deploy/*.sh
sudo touch versions/deploy.log
sudo chmod 666 versions/deploy.log
sudo chown -R root:root versions
lsof /var/www/tarot/scripts/deploy/status.sh

cd /var/www/tarot
unzip dist.zip -d dist
unzip server.zip -d server
cd server
pnpm install

pm2 start index.js --name tarot-backend
pm2 status
pm2 logs tarot-backend

<!-- 配置SSL证书 -->
# 在服务器上创建存放证书的目录
sudo mkdir -p /etc/nginx/ssl
将下载的证书文件（应该包含 .pem 和 .key 文件）上传到服务器的 /etc/nginx/ssl 目录
设置证书文件权限：
sudo chmod 644 /etc/nginx/ssl/*.pem 
sudo chmod 600 /etc/nginx/ssl/*.key
sudo chown root:root /etc/nginx/ssl/*
更新后要重启Nginx：
sudo systemctl restart nginx


2. 配置 Nginx：

Nginx只能有一个配置文件，因此需要删除原有的配置文件。
sudo rm /etc/nginx/sites-enabled/*
sudo rm /etc/nginx/conf.d/*.conf

# 创建配置文件
sudo nano /etc/nginx/conf.d/tarot.conf

cat /etc/nginx/conf.d/tarot.conf

# HTTP 服务器配置
server {
    listen 80;
    server_name tarotqa.com www.tarotqa.com;

    # 强制跳转HTTPS
    return 301 https://$server_name$request_uri;
}

# CDN HTTP 配置 - 强制 HTTPS
server {
    listen 80;
    server_name cdn.tarotqa.com;

    # 强制跳转HTTPS
    return 301 https://$server_name$request_uri;
}

# CDN HTTPS 配置
server {
    listen 443 ssl http2; # 添加http2支持
    server_name cdn.tarotqa.com;

    # SSL 证书配置
    ssl_certificate /etc/nginx/ssl/tarotqa.com.pem;
    ssl_certificate_key /etc/nginx/ssl/tarotqa.com.key;
    ssl_session_timeout 5m;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_prefer_server_ciphers on;

    # 视频文件目录
    location /public/ {
        alias /var/www/tarot/public/;

        # 正确的文件 MIME 类型
        types {
            video/webm webm;
            image/webp webp;
        }

        # 允许跨域访问
        add_header Access-Control-Allow-Origin "*";
        add_header Access-Control-Allow-Methods "GET, OPTIONS";

        # 缓存设置
        expires 30d;
        add_header Cache-Control "public, no-transform";

        # 开启 sendfile
        sendfile on;
        tcp_nopush on;

        # 图片和视频的压缩设置
        gzip on;
        gzip_types image/webp;
        gzip_min_length 256;
        gzip_comp_level 6;
        gzip_vary on;

        # 大文件优化
        proxy_max_temp_file_size 0;
        proxy_buffering off;

        # 针对图片的特殊优化
        location ~* \.webp$ {
            add_header Vary Accept;
            access_log off;
            log_not_found off;
            expires max;
        }
    }

    # 视频文件目录
    location /dist/ {
        alias /var/www/tarot/dist/;

        # 正确的文件 MIME 类型
        types {
            video/webm webm;
            image/webp webp;
        }

        # 允许跨域访问
        add_header Access-Control-Allow-Origin "*";
        add_header Access-Control-Allow-Methods "GET, OPTIONS";

        # 缓存设置
        expires 30d;
        add_header Cache-Control "public, no-transform";

        # 开启 sendfile
        sendfile on;
        tcp_nopush on;

        # 图片和视频的压缩设置
        gzip on;
        gzip_types image/webp;
        gzip_min_length 256;
        gzip_comp_level 6;
        gzip_vary on;

        # 大文件优化
        proxy_max_temp_file_size 0;
        proxy_buffering off;

        # 针对图片的特殊优化
        location ~* \.webp$ {
            add_header Vary Accept;
            access_log off;
            log_not_found off;
            expires max;
        }
    }

}
# HTTPS www 重定向配置
server {
    listen 443 ssl http2; # 添加http2支持
    server_name www.tarotqa.com;

    # SSL 证书配置
    ssl_certificate /etc/nginx/ssl/tarotqa.com.pem;
    ssl_certificate_key /etc/nginx/ssl/tarotqa.com.key;

    # 重定向到非www域名
    return 301 https://tarotqa.com$request_uri;
}

# WebP 支持检测
map $http_accept $webp_suffix {
    default   "";
    "~*webp"  ".webp";
}

# HTTPS 主服务器配置
server {
    listen 443 ssl http2; # 添加http2支持
    server_name tarotqa.com;

    # SSL 证书配置
    ssl_certificate /etc/nginx/ssl/tarotqa.com.pem;
    ssl_certificate_key /etc/nginx/ssl/tarotqa.com.key;
    ssl_session_timeout 5m;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_prefer_server_ciphers on;

    # Gzip 压缩配置
    gzip on;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript image/svg+xml;
    gzip_min_length 1000;
    gzip_comp_level 6;
    gzip_vary on;
    gzip_proxied any;
    gzip_disable "MSIE [1-6]\.";
    gzip_static on;

    # 日志配置
    access_log /var/log/nginx/tarot_access.log;
    error_log /var/log/nginx/tarot_error.log debug;

    # 前端静态文件
    root /var/www/tarot/dist;
    index index.html;

    # 添加 /public 目录的特殊处理
    location /public/ {
        alias /var/www/tarot/public/;  # 确保这个路径是正确的

        # 正确的文件 MIME 类型
        types {
            video/webm webm;
            image/webp webp;
        }

        # 允许跨域访问
        add_header Access-Control-Allow-Origin "*";
        add_header Access-Control-Allow-Methods "GET, OPTIONS";

        # 缓存设置
        expires 30d;
        add_header Cache-Control "public, no-transform";

        # 开启 sendfile
        sendfile on;
        tcp_nopush on;

        # 图片和视频的压缩设置
        gzip on;
        gzip_types image/webp;
        gzip_min_length 256;
        gzip_comp_level 6;
        gzip_vary on;

        # 大文件优化
        proxy_max_temp_file_size 0;
        proxy_buffering off;

        # 针对图片的特殊优化
        location ~* \.webp$ {
            add_header Vary Accept;
            access_log off;
            log_not_found off;
            expires max;
        }
    }


    # 添加 /dist 目录的特殊处理
    location /dist/ {
        alias /var/www/tarot/dist/;  # 确保这个路径是正确的

        # 正确的文件 MIME 类型
        types {
            video/webm webm;
            image/webp webp;
        }

        # 允许跨域访问
        add_header Access-Control-Allow-Origin "*";
        add_header Access-Control-Allow-Methods "GET, OPTIONS";

        # 缓存设置
        expires 30d;
        add_header Cache-Control "public, no-transform";

        # 开启 sendfile
        sendfile on;
        tcp_nopush on;

        # 图片和视频的压缩设置
        gzip on;
        gzip_types image/webp;
        gzip_min_length 256;
        gzip_comp_level 6;
        gzip_vary on;

        # 大文件优化
        proxy_max_temp_file_size 0;
        proxy_buffering off;

        # 针对图片的特殊优化
        location ~* \.webp$ {
            add_header Vary Accept;
            access_log off;
            log_not_found off;
            expires max;
        }
    }

    # 删除静态隐私政策HTML文件特殊处理和重定向
    # React应用路由 - 需要在通用匹配之前
    location /home {
        try_files $uri $uri/ /index.html;
        add_header Cache-Control "no-cache, no-store, must-revalidate";
        add_header Pragma "no-cache";
        add_header Expires 0;
    }

    # 图片资源处理（支持 WebP）
    location ~* \.(png|jpe?g)$ {
        add_header Vary Accept;
        try_files $uri$webp_suffix $uri =404;
        expires 30d;
        add_header Cache-Control "public, no-transform";

        # 开启 gzip 压缩
        gzip_static on;
    }

    # WebP 图片专门处理
    location ~* \.webp$ {
        expires 30d;
        add_header Cache-Control "public, no-transform";
        try_files $uri =404;
    }

    # 其他静态资源缓存 - 移到 /landing/ 配置后面
    location ~* \.(js|css|ico|svg)$ {
        expires 30d;
        add_header Cache-Control "public, no-transform";
        try_files $uri =404;

        # 开启 gzip 压缩
        gzip_static on;
    }

    # 字体文件处理
    location ~* \.(woff|woff2|ttf|eot)$ {
        expires 30d;
        add_header Cache-Control "public, no-transform";
        add_header Access-Control-Allow-Origin "*";
        try_files $uri =404;
    }

    # 支付回调专用处理
    location = /api/payment/wechat/notify {
        proxy_pass http://127.0.0.1:5000/api/payment/wechat/notify;

        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # 不处理 CORS，因为这是服务器间的通信
        proxy_pass_request_headers on;
    }

    # 支付宝回调专用处理
    location = /api/payment/alipay/notify {
        proxy_pass http://127.0.0.1:5000/api/payment/alipay/notify;

        # 添加调试日志
        access_log /var/log/nginx/alipay_notify_access.log combined buffer=512k;
        error_log /var/log/nginx/alipay_notify_error.log debug;

        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # 增加超时时间
        proxy_connect_timeout 60;
        proxy_send_timeout 60;
        proxy_read_timeout 60;

        # 移除所有 CORS 相关头部
        proxy_hide_header 'Access-Control-Allow-Origin';
        proxy_hide_header 'Access-Control-Allow-Methods';
        proxy_hide_header 'Access-Control-Allow-Headers';
        proxy_hide_header 'Access-Control-Expose-Headers';
        proxy_hide_header 'Access-Control-Max-Age';

        # 确保请求体能够正确传递
        client_max_body_size 8m;
        client_body_buffer_size 128k;
    }

    # API 反向代理
    location /api/ {
        proxy_pass http://127.0.0.1:5000/;

        # 添加这两行以支持流式响应
        proxy_buffering off;
        proxy_max_temp_file_size 0;
        
        # 增加超时时间以支持长时间连接
        proxy_read_timeout 300s;

        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # 移除后端设置的 CORS 头
        proxy_hide_header 'Access-Control-Allow-Origin';
        proxy_hide_header 'Access-Control-Allow-Methods';
        proxy_hide_header 'Access-Control-Allow-Headers';
        proxy_hide_header 'Access-Control-Expose-Headers';
        proxy_hide_header 'Access-Control-Max-Age';

        # 设置允许的域名
        set $cors_origin "";
        if ($http_origin = "https://tarotqa.com") {
            set $cors_origin "https://tarotqa.com";
        }

        # CORS 预检请求处理
        if ($request_method = 'OPTIONS') {
            add_header 'Access-Control-Allow-Origin' $cors_origin always;
            add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS, PUT, DELETE, PATCH' always;
            add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization' always;
            add_header 'Access-Control-Max-Age' 1728000 always;
            add_header 'Content-Type' 'text/plain; charset=utf-8' always;
            add_header 'Content-Length' 0 always;
            return 204;
        }

        # 正常请求的 CORS 处理
        add_header 'Access-Control-Allow-Origin' $cors_origin always;
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS, PUT, DELETE, PATCH' always;
        add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization' always;
        add_header 'Access-Control-Expose-Headers' 'Content-Length,Content-Range' always;
        add_header 'Access-Control-Allow-Credentials' 'true' always;
    }

    # 通用路由处理 - 放在最后
    location / {
        try_files $uri $uri/ /index.html;
        add_header Cache-Control "no-cache, no-store, must-revalidate";
        add_header Pragma "no-cache";
        add_header Expires 0;
    }
}

# 1. 测试配置
sudo nginx -t

# 2. 重启 Nginx
sudo systemctl restart nginx

# 3. 检查 Nginx 状态
sudo systemctl status nginx

# 4. 确保权限正确
sudo chown -R www-data:www-data /var/www/tarot
sudo chmod -R 755 /var/www/tarot

# 检查防火墙状态和端口
sudo ufw status verbose
sudo ufw allow 22/tcp

# 1. 测试本地后端
curl -X POST http://localhost:5000/api/auth/login -H "Content-Type: application/json" -d '{"email":"<EMAIL>","password":"test"}'

# 2. 测试 Nginx 代理
curl -X POST http://localhost/api/auth/login -H "Content-Type: application/json" -d '{"email":"<EMAIL>","password":"test"}'

# 3. 检查 Nginx 错误日志
tail -f /var/log/nginx/tarot_error.log



常见问题：
<!-- 让我们统一所有环境的配置。这些配置文件中不应该有重复的 /api 前缀，因为：
后端路由已经包含了 /api 前缀（在 server/index.js 中）
Nginx 配置中也处理了 /api 路径的代理 -->
<!-- 记住，/api 前缀已经在：

后端路由中定义（app.use('/api/reading', readingRoutes)）
Nginx 配置中处理（location /api/ { ... }） -->

<!-- 域名解析：确保在更换ECS实例后，及时更新域名解析记录，将域名指向新的ECS实例。 -->

数据库RDS白名单应该设置公网ip：关闭vpn后，curl -UseBasicParsing ifconfig.me


可能后端存在未安装的依赖，导致无法启动，可以查看后端日志：
pm2 logs tarot-backend --lines 50 | cat
pm2 stop tarot-backend
pnpm install
pm2 start index.js --name tarot-backend


cd /var/www/tarot
rm -rf "/var/www/tarot/dist/"
rm -rf "/var/www/tarot/dist.zip"
rm -rf "/var/www/tarot/server/"
rm -rf "/var/www/tarot/server.zip"

unzip dist.zip -d dist
unzip server.zip -d server  

sudo systemctl restart nginx  # 重启 nginx
pm2 restart tarot-backend
pm2 logs tarot-backend --lines 200 | cat 


# CDN
在服务器上创建对应的目录结构：
mkdir -p /var/www/tarot/public/

将视频文件上传到正确的位置：
/var/www/tarot/public/v1.webm
/var/www/tarot/public/v2.webm
/var/www/tarot/public/v3.webm

设置正确的权限：
chmod -R 755 /var/www/tarot/public/
chown -R www-data:www-data /var/www/tarot/public/

重启Nginx使配置生效：
sudo nginx -t  # 测试配置是否正确
sudo systemctl restart nginx  # 重启Nginx

https://cdn.tarotqa.com/public/home-images-Claire.webp


# 日志轮转

# 卸载当前的pm2-logrotate模块
pm2 uninstall pm2-logrotate

# 重新安装pm2-logrotate
pm2 install pm2-logrotate

# 配置日志轮转
pm2 set pm2-logrotate:max_size 10M
pm2 set pm2-logrotate:retain 30
pm2 set pm2-logrotate:compress false
pm2 set pm2-logrotate:dateFormat YYYY-MM-DD
pm2 set pm2-logrotate:workerInterval 30
pm2 set pm2-logrotate:rotateInterval "0 0 * * *"
pm2 set pm2-logrotate:rotateModule false

# 清空当前日志并重新加载
pm2 flush
pm2 reloadLogs

pm2 uninstall pm2-logrotate


sudo mv /etc/logrotate.d/pm2-root /etc/logrotate.d/pm2-root.disabled.bak
rm -f /root/.pm2/logs/tarot-backend-error-0.log.1
pm2 restart tarot-backend
pm2 save