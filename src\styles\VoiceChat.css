/* 语音聊天页面样式 */
.voice-chat-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-height: 100vh;
  padding: 2rem 1rem;
  position: relative;
  overflow: hidden;
  z-index: 1;
}

/* 页面标题区域 */
.voice-chat-title-section {
  width: 100%;
  max-width: 1200px;
  text-align: center;
  position: relative;
  z-index: 1;
}

.voice-chat-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  background: linear-gradient(90deg, #6366f1, #8b5cf6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  text-align: center;
  background-clip: text;
  margin-bottom: 0.5rem;
  line-height: 1.2;
}

.dark .voice-chat-title {
  background: linear-gradient(90deg, #818cf8, #a78bfa);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.voice-chat-subtitle {
  font-size: 1.25rem;
  color: var(--text-secondary, #4b5563);
  max-width: 800px;
  margin: 0 auto 2rem;
  text-align: center;
  line-height: 1.6;
  font-style: italic;
}

.dark .voice-chat-subtitle {
  color: #a78bfa;
}

/* 移除装饰元素相关样式 */
/* .voice-chat-decorations {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: -1;
  overflow: hidden;
}

.voice-chat-decoration {
  position: absolute;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(139, 92, 246, 0.2) 0%, rgba(139, 92, 246, 0) 70%);
  animation: float 15s infinite ease-in-out;
}

.voice-chat-decoration:nth-child(1) {
  width: 400px;
  height: 400px;
  top: -100px;
  right: -100px;
  animation-delay: 0s;
}

.voice-chat-decoration:nth-child(2) {
  width: 300px;
  height: 300px;
  bottom: -50px;
  left: -50px;
  animation-delay: 5s;
}

.voice-chat-decoration:nth-child(3) {
  width: 200px;
  height: 200px;
  bottom: 30%;
  right: 10%;
  animation-delay: 10s;
}

@keyframes float {
  0% {
    transform: translateY(0) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(5deg);
  }
  100% {
    transform: translateY(0) rotate(0deg);
  }
} */

/* 开始按钮区域 */
.voice-chat-start {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin: 2rem 0;
  position: relative;
  z-index: 1;
  padding: 3rem;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  border-radius: 24px;
  width: 100%;
  max-width: 600px;
  transition: all 0.3s ease;
  border: 1px solid rgba(236, 72, 153, 0.3);
  box-shadow: 
    0 0 0 1px rgba(168, 85, 247, 0.2),
    0 0 15px rgba(168, 85, 247, 0.15),
    0 0 30px rgba(236, 72, 153, 0.15),
    inset 0 0 15px rgba(168, 85, 247, 0.1);
}

.dark .voice-chat-start {
  background: rgba(13, 12, 15, 0.95);
  border: 1px solid rgba(236, 72, 153, 0.3);
}

.voice-chat-start::before {
  content: '';
  position: absolute;
  inset: -1px;
  padding: 1px;
  background: linear-gradient(
    135deg,
    rgba(168, 85, 247, 0.3),
    rgba(236, 72, 153, 0.3)
  );
  -webkit-mask: 
    linear-gradient(#fff 0 0) content-box, 
    linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
  pointer-events: none;
  border-radius: 24px;
}

.dark .voice-chat-start::before {
  background: linear-gradient(
    135deg,
    rgba(168, 85, 247, 0.5),
    rgba(236, 72, 153, 0.5)
  );
}

.voice-chat-start::after {
  content: '';
  position: absolute;
  inset: 0;
  background: 
    radial-gradient(circle at -30% -30%, rgba(168, 85, 247, 0.05), transparent 50%),
    radial-gradient(circle at 130% 130%, rgba(236, 72, 153, 0.05), transparent 50%);
  pointer-events: none;
  border-radius: 24px;
}

.dark .voice-chat-start::after {
  background: 
    radial-gradient(circle at -30% -30%, rgba(168, 85, 247, 0.15), transparent 70%),
    radial-gradient(circle at 130% 130%, rgba(236, 72, 153, 0.15), transparent 70%);
}

.voice-chat-start-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  width: 100%;
}

.voice-chat-icon {
  width: 120px;
  height: 120px;
  margin-bottom: 2rem;
  border-radius: 50%;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 10px 20px rgba(99, 102, 241, 0.3);
}

.voice-chat-icon svg {
  width: 60px;
  height: 60px;
  color: white;
}

.voice-chat-start-title {
  font-size: 1.75rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: var(--text-primary, #111827);
}

.dark .voice-chat-start-title {
  color: var(--text-primary-dark, #f9fafb);
}

.voice-chat-start-description {
  font-size: 1.1rem;
  color: var(--text-secondary, #4b5563);
  margin-bottom: 2.5rem;
  line-height: 1.6;
  max-width: 400px;
}

.dark .voice-chat-start-description {
  color: var(--text-secondary-dark, #9ca3af);
}

.voice-chat-button {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem 2.5rem;
  border-radius: 50px;
  border: none;
  background: linear-gradient(90deg, #6366f1, #8b5cf6);
  color: white;
  font-size: 1.25rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 10px 20px rgba(99, 102, 241, 0.3);
  position: relative;
  overflow: hidden;
}

.voice-chat-button:before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: all 0.6s ease;
}

.voice-chat-button:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 25px rgba(99, 102, 241, 0.4);
}

.voice-chat-button:hover:before {
  left: 100%;
}

.voice-chat-button:disabled {
  background: linear-gradient(90deg, #93c5fd, #c4b5fd);
  cursor: not-allowed;
  transform: none;
  box-shadow: 0 5px 15px rgba(99, 102, 241, 0.2);
}

.voice-chat-button-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 1rem;
}

.voice-chat-button-icon svg {
  width: 24px;
  height: 24px;
  fill: white;
}

/* 聊天会话样式 */
.voice-chat-session {
  display: flex;
  flex-direction: column;
  width: 100%;
  max-width: 1000px;
  height: 80vh;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-radius: 24px;
  overflow: hidden;
  position: relative;
  z-index: 1;
  border: 1px solid rgba(236, 72, 153, 0.3);
  box-shadow: 
    0 0 0 1px rgba(168, 85, 247, 0.2),
    0 0 15px rgba(168, 85, 247, 0.15),
    0 0 30px rgba(236, 72, 153, 0.15),
    inset 0 0 15px rgba(168, 85, 247, 0.1);
}

.dark .voice-chat-session {
  background: rgba(13, 12, 15, 0.95);
  border: 1px solid rgba(236, 72, 153, 0.3);
}

.voice-chat-session::before {
  content: '';
  position: absolute;
  inset: -1px;
  padding: 1px;
  background: linear-gradient(
    135deg,
    rgba(168, 85, 247, 0.3),
    rgba(236, 72, 153, 0.3)
  );
  -webkit-mask: 
    linear-gradient(#fff 0 0) content-box, 
    linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
  pointer-events: none;
  border-radius: 24px;
  z-index: -1;
}

.dark .voice-chat-session::before {
  background: linear-gradient(
    135deg,
    rgba(168, 85, 247, 0.5),
    rgba(236, 72, 153, 0.5)
  );
}

.voice-chat-session::after {
  content: '';
  position: absolute;
  inset: 0;
  background: 
    radial-gradient(circle at -30% -30%, rgba(168, 85, 247, 0.05), transparent 50%),
    radial-gradient(circle at 130% 130%, rgba(236, 72, 153, 0.05), transparent 50%);
  pointer-events: none;
  border-radius: 24px;
  z-index: -1;
}

.dark .voice-chat-session::after {
  background: 
    radial-gradient(circle at -30% -30%, rgba(168, 85, 247, 0.15), transparent 70%),
    radial-gradient(circle at 130% 130%, rgba(236, 72, 153, 0.15), transparent 70%);
}

.voice-chat-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem 2rem;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  background: rgba(255, 255, 255, 0.5);
  backdrop-filter: blur(5px);
}

.dark .voice-chat-header {
  background: rgba(10, 10, 15, 0.5);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.voice-chat-header-left {
  display: flex;
  align-items: center;
}

.voice-chat-header-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 1rem;
  border: 2px solid #6366f1;
}

.voice-chat-header h1 {
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0;
  color: var(--text-primary, #111827);
  display: flex;
  align-items: center;
}

.dark .voice-chat-header h1 {
  color: var(--text-primary-dark, #f9fafb);
}

.voice-chat-status-indicator {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: #10b981;
  margin-left: 0.75rem;
  position: relative;
}

.voice-chat-status-indicator:after {
  content: '';
  position: absolute;
  top: -3px;
  left: -3px;
  right: -3px;
  bottom: -3px;
  border-radius: 50%;
  background-color: rgba(16, 185, 129, 0.3);
  animation: pulse 1.5s infinite;
}

.voice-chat-close {
  background: rgba(239, 68, 68, 0.1);
  border: none;
  color: #ef4444;
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 600;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
}

.voice-chat-close:hover {
  background: rgba(239, 68, 68, 0.2);
}

.voice-chat-close svg {
  width: 18px;
  height: 18px;
  margin-right: 0.5rem;
}

.voice-chat-content {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.voice-chat-sidebar {
  width: 300px;
  border-right: 1px solid rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.05);
}

.dark .voice-chat-sidebar {
  border-right: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(13, 12, 15, 0.5);
}

.voice-chat-avatar-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 1.5rem;
}

.voice-chat-avatar {
  position: relative;
  width: 150px;
  height: 150px;
  border-radius: 50%;
  overflow: hidden;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
  border: 4px solid #6366f1;
  margin-bottom: 1.5rem;
}

.dark .voice-chat-avatar {
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
  border: 4px solid #6366f1;
}

.voice-chat-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.voice-chat-assistant-name {
  font-size: 1.25rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  color: var(--text-primary, #111827);
}

.dark .voice-chat-assistant-name {
  color: var(--text-primary-dark, #f9fafb);
}

.voice-chat-assistant-title {
  font-size: 0.875rem;
  color: var(--text-secondary, #4b5563);
  margin-bottom: 1.5rem;
}

.dark .voice-chat-assistant-title {
  color: var(--text-secondary-dark, #9ca3af);
}

.voice-chat-status {
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(16, 185, 129, 0.1);
  padding: 0.5rem 1rem;
  border-radius: 50px;
  font-size: 0.875rem;
  color: #10b981;
  font-weight: 500;
}

.dark .voice-chat-status {
  background: rgba(16, 185, 129, 0.2);
}

.voice-chat-status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #10b981;
  margin-right: 0.5rem;
  animation: pulse 1.5s infinite;
}

.voice-chat-status.recording {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

.dark .voice-chat-status.recording {
  background: rgba(239, 68, 68, 0.2);
}

.voice-chat-status.recording .voice-chat-status-dot {
  background-color: #ef4444;
}

.voice-chat-sidebar-divider {
  height: 1px;
  background: rgba(0, 0, 0, 0.1);
  margin: 1.5rem 0;
  width: 100%;
}

.dark .voice-chat-sidebar-divider {
  background: rgba(255, 255, 255, 0.1);
}

.voice-chat-sidebar-section {
  margin-bottom: 1.5rem;
}

.voice-chat-sidebar-title {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-secondary, #4b5563);
  margin-bottom: 0.75rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.dark .voice-chat-sidebar-title {
  color: var(--text-secondary-dark, #9ca3af);
}

.voice-chat-sidebar-content {
  font-size: 0.875rem;
  color: var(--text-primary, #111827);
  line-height: 1.5;
}

.dark .voice-chat-sidebar-content {
  color: var(--text-primary-dark, #f9fafb);
}

.voice-chat-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.02);
}

.dark .voice-chat-main {
  background: rgba(13, 12, 15, 0.3);
}

.voice-chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.voice-chat-message {
  display: flex;
  flex-direction: column;
  max-width: 80%;
}

.voice-chat-message-header {
  display: flex;
  align-items: center;
  margin-bottom: 0.5rem;
}

.voice-chat-message-avatar {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 0.75rem;
}

.voice-chat-message-name {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-secondary, #4b5563);
}

.dark .voice-chat-message-name {
  color: var(--text-secondary-dark, #9ca3af);
}

.voice-chat-message-bubble {
  padding: 1rem 1.25rem;
  border-radius: 16px;
  position: relative;
  line-height: 1.6;
}

.voice-chat-message.user {
  align-self: flex-end;
}

.voice-chat-message.user .voice-chat-message-bubble {
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  color: white;
  border-bottom-right-radius: 4px;
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.2);
}

.voice-chat-message.assistant {
  align-self: flex-start;
}

.voice-chat-message.assistant .voice-chat-message-bubble {
  background: rgba(243, 244, 246, 0.8);
  color: var(--text-primary, #111827);
  border-bottom-left-radius: 4px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.dark .voice-chat-message.assistant .voice-chat-message-bubble {
  background: rgba(31, 41, 55, 0.8);
  color: var(--text-primary-dark, #f9fafb);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.voice-chat-controls {
  padding: 1.5rem;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  align-items: center;
  background: rgba(255, 255, 255, 0.5);
  backdrop-filter: blur(5px);
}

.dark .voice-chat-controls {
  background: rgba(13, 12, 15, 0.5);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.voice-chat-timer {
  font-size: 0.875rem;
  color: var(--text-secondary, #4b5563);
  margin-bottom: 1rem;
  font-weight: 500;
}

.dark .voice-chat-timer {
  color: var(--text-secondary-dark, #9ca3af);
}

.voice-chat-buttons {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 2rem;
}

.voice-chat-control-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 56px;
  height: 56px;
  border-radius: 50%;
  border: none;
  background-color: white;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.dark .voice-chat-control-button {
  background-color: #13131F;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.voice-chat-control-button svg {
  width: 24px;
  height: 24px;
  color: #4b5563;
  transition: all 0.2s ease;
}

.dark .voice-chat-control-button svg {
  color: #9ca3af;
}

.voice-chat-control-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.dark .voice-chat-control-button:hover {
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.4);
}

.voice-chat-control-button:hover svg {
  color: #6366f1;
}

.voice-chat-control-button.record {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  box-shadow: 0 10px 20px rgba(99, 102, 241, 0.3);
  position: relative;
  overflow: hidden;
}

.voice-chat-control-button.record::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: all 0.6s ease;
}

.voice-chat-control-button.record:hover::before {
  left: 100%;
}

.voice-chat-control-button.record svg {
  width: 32px;
  height: 32px;
  color: white;
}

.voice-chat-control-button.record:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 30px rgba(99, 102, 241, 0.4);
}

.voice-chat-control-button.record.active {
  background: linear-gradient(135deg, #ef4444, #f87171);
  box-shadow: 0 10px 20px rgba(239, 68, 68, 0.3);
  animation: pulse-record 1.5s infinite;
}

.voice-chat-control-button.record.active:hover {
  box-shadow: 0 15px 30px rgba(239, 68, 68, 0.4);
}

.voice-chat-control-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none !important;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1) !important;
}

.voice-chat-control-button.record:disabled {
  background: linear-gradient(135deg, #9ca3af, #6b7280);
  box-shadow: 0 5px 10px rgba(156, 163, 175, 0.2) !important;
  animation: none;
}

.voice-chat-control-button:disabled svg {
  color: #6b7280 !important;
}

/* 设备选择器样式 */
.voice-chat-device-selector {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.voice-chat-device-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
  margin-bottom: 0.25rem;
}

.dark .voice-chat-device-label {
  color: #d1d5db;
}

.voice-chat-device-select {
  width: 100%;
  padding: 0.5rem 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  background-color: white;
  color: #374151;
  font-size: 0.875rem;
  transition: all 0.2s ease;
  cursor: pointer;
}

.voice-chat-device-select:focus {
  outline: none;
  border-color: #6366f1;
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.voice-chat-device-select:disabled {
  background-color: #f3f4f6;
  color: #9ca3af;
  cursor: not-allowed;
}

.dark .voice-chat-device-select {
  background-color: #1f2937;
  border-color: #374151;
  color: #d1d5db;
}

.dark .voice-chat-device-select:focus {
  border-color: #8b5cf6;
  box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1);
}

.dark .voice-chat-device-select:disabled {
  background-color: #111827;
  color: #6b7280;
}

.voice-chat-device-info {
  font-size: 0.75rem;
  color: #6b7280;
  font-style: italic;
}

.dark .voice-chat-device-info {
  color: #9ca3af;
}

.voice-chat-refresh-devices {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  background-color: #f3f4f6;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  color: #374151;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.voice-chat-refresh-devices:hover {
  background-color: #e5e7eb;
  border-color: #9ca3af;
}

.voice-chat-refresh-devices:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.voice-chat-refresh-devices svg {
  width: 16px;
  height: 16px;
}

.dark .voice-chat-refresh-devices {
  background-color: #374151;
  border-color: #4b5563;
  color: #d1d5db;
}

.dark .voice-chat-refresh-devices:hover {
  background-color: #4b5563;
  border-color: #6b7280;
}

/* 当前设备显示 */
.voice-chat-current-device {
  margin-top: 0.5rem;
  padding: 0.5rem;
  background-color: rgba(99, 102, 241, 0.1);
  border: 1px solid rgba(99, 102, 241, 0.2);
  border-radius: 0.375rem;
  font-size: 0.75rem;
}

.dark .voice-chat-current-device {
  background-color: rgba(139, 92, 246, 0.1);
  border-color: rgba(139, 92, 246, 0.2);
}

.voice-chat-current-device-label {
  color: #6b7280;
  margin-right: 0.5rem;
}

.dark .voice-chat-current-device-label {
  color: #9ca3af;
}

.voice-chat-current-device-name {
  color: #6366f1;
  font-weight: 500;
}

.dark .voice-chat-current-device-name {
  color: #8b5cf6;
}

/* 音频级别显示 */
.voice-chat-audio-level {
  margin-top: 0.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.voice-chat-audio-level-label {
  font-size: 0.75rem;
  color: #6b7280;
  white-space: nowrap;
}

.dark .voice-chat-audio-level-label {
  color: #9ca3af;
}

.voice-chat-audio-level-bar {
  flex: 1;
  height: 8px;
  background-color: #e5e7eb;
  border-radius: 4px;
  overflow: hidden;
  position: relative;
}

.dark .voice-chat-audio-level-bar {
  background-color: #374151;
}

.voice-chat-audio-level-fill {
  height: 100%;
  background: linear-gradient(90deg, #10b981, #34d399, #fbbf24, #f59e0b, #ef4444);
  border-radius: 4px;
  transition: width 0.1s ease;
  min-width: 2px;
}

.voice-chat-audio-level-value {
  font-size: 0.75rem;
  color: #6366f1;
  font-weight: 500;
  min-width: 35px;
  text-align: right;
}

.dark .voice-chat-audio-level-value {
  color: #8b5cf6;
}

/* 麦克风状态指示器 */
.voice-chat-mic-indicator {
  position: fixed;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: linear-gradient(135deg, #ef4444, #f87171);
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 2rem;
  box-shadow: 0 10px 25px rgba(239, 68, 68, 0.3);
  z-index: 1000;
  animation: pulse-indicator 2s infinite;
  font-weight: 500;
  font-size: 0.875rem;
}

.voice-chat-mic-indicator-icon {
  display: flex;
  align-items: center;
  justify-content: center;
}

.voice-chat-mic-indicator-icon svg {
  width: 20px;
  height: 20px;
  color: white;
}

.voice-chat-mic-indicator-text {
  white-space: nowrap;
}

@keyframes pulse-indicator {
  0%, 100% {
    box-shadow: 0 10px 25px rgba(239, 68, 68, 0.3);
  }
  50% {
    box-shadow: 0 10px 25px rgba(239, 68, 68, 0.6);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .voice-chat-mic-indicator {
    top: 10px;
    left: 10px;
    right: 10px;
    transform: none;
    font-size: 0.75rem;
    padding: 0.5rem 1rem;
  }

  .voice-chat-mic-indicator-text {
    white-space: normal;
    text-align: center;
  }
}

/* 动画 */
@keyframes pulse {
  0% {
    transform: scale(0.95);
    opacity: 0.8;
  }
  50% {
    transform: scale(1.05);
    opacity: 1;
  }
  100% {
    transform: scale(0.95);
    opacity: 0.8;
  }
}

@keyframes pulse-record {
  0% {
    transform: scale(0.95);
    box-shadow: 0 10px 20px rgba(239, 68, 68, 0.3);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 15px 30px rgba(239, 68, 68, 0.4);
  }
  100% {
    transform: scale(0.95);
    box-shadow: 0 10px 20px rgba(239, 68, 68, 0.3);
  }
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .voice-chat-content {
    flex-direction: column;
  }
  
  .voice-chat-sidebar {
    width: 100%;
    border-right: none;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    padding: 1rem;
  }
  
  .dark .voice-chat-sidebar {
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  }
  
  .voice-chat-avatar-container {
    flex-direction: row;
    align-items: center;
    margin-bottom: 0;
  }
  
  .voice-chat-avatar {
    width: 80px;
    height: 80px;
    margin-bottom: 0;
    margin-right: 1.5rem;
  }
  
  .voice-chat-sidebar-info {
    flex: 1;
  }
  
  .voice-chat-sidebar-divider,
  .voice-chat-sidebar-section {
    display: none;
  }
}

@media (max-width: 768px) {
  .voice-chat-container {
    padding: 1rem;
  }
  
  .voice-chat-title {
    font-size: 2rem;
  }
  
  .voice-chat-subtitle {
    font-size: 1rem;
  }
  
  .voice-chat-start {
    padding: 2rem 1.5rem;
  }
  
  .voice-chat-icon {
    width: 100px;
    height: 100px;
  }
  
  .voice-chat-icon svg {
    width: 50px;
    height: 50px;
  }
  
  .voice-chat-start-title {
    font-size: 1.5rem;
  }
  
  .voice-chat-start-description {
    font-size: 1rem;
  }
  
  .voice-chat-session {
    height: 85vh;
  }
  
  .voice-chat-header {
    padding: 1rem;
  }
  
  .voice-chat-header h1 {
    font-size: 1.25rem;
  }
  
  .voice-chat-messages {
    padding: 1rem;
  }
  
  .voice-chat-message {
    max-width: 90%;
  }
  
  .voice-chat-controls {
    padding: 1rem;
  }
  
  .voice-chat-buttons {
    gap: 1rem;
  }
  
  .voice-chat-control-button {
    width: 48px;
    height: 48px;
  }
  
  .voice-chat-control-button.record {
    width: 64px;
    height: 64px;
  }
  
  .voice-chat-control-button.record svg {
    width: 28px;
    height: 28px;
  }
} 