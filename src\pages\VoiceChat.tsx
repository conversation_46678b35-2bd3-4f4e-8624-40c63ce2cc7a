import React, { useState, useEffect, useRef } from 'react';
import { Helmet } from 'react-helmet-async';
import '../styles/VoiceChat.css';
import CdnLazyImage from '../components/CdnLazyImage';
// import LanguageLink from '../components/LanguageLink';
import { useTranslation } from 'react-i18next';
import axios from 'axios';
import { useTheme } from '../contexts/ThemeContext';
import LandingBackground from '../components/LandingBackground';

const VoiceChat: React.FC = () => {
  const { t } = useTranslation();
  const { theme } = useTheme();
  const [isConnected, setIsConnected] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [sessionId, setSessionId] = useState<string | null>(null);
  const [messages, setMessages] = useState<Array<{text: string, isUser: boolean}>>([]);
//   const [setCurrentMessage] = useState('');
  const [isRecording, setIsRecording] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [audioDevices, setAudioDevices] = useState<MediaDeviceInfo[]>([]);
  const [selectedDeviceId, setSelectedDeviceId] = useState<string>('');
  const [showDeviceSelector, setShowDeviceSelector] = useState(false);
  const [currentDeviceLabel, setCurrentDeviceLabel] = useState<string>('');
  const avatarRef = useRef<HTMLDivElement>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const wsRef = useRef<WebSocket | null>(null);
  const audioContextRef = useRef<AudioContext | null>(null);
  const mediaStreamRef = useRef<MediaStream | null>(null);
  const processorRef = useRef<ScriptProcessorNode | null>(null);
  const microphoneRef = useRef<MediaStreamAudioSourceNode | null>(null);
  const analyserRef = useRef<AnalyserNode | null>(null);
  const audioLevelRef = useRef<number>(0);
  const lastAudioLogTime = useRef<number>(0);
  const audioLevelUpdateTimer = useRef<NodeJS.Timeout | null>(null);
  const [audioLevel, setAudioLevel] = useState<number>(0);
  
  // API基础URL
  const apiBaseUrl = `${window.location.protocol}//${window.location.hostname}:5000`;

  // 计算音频级别
  const calculateAudioLevel = (audioData: Float32Array): number => {
    let sum = 0;
    for (let i = 0; i < audioData.length; i++) {
      sum += audioData[i] * audioData[i];
    }
    const rms = Math.sqrt(sum / audioData.length);
    return Math.min(1, rms * 10); // 放大并限制在0-1之间
  };

  // 检测音频输入并记录设备信息
  const logAudioInput = (level: number, deviceLabel: string) => {
    const now = Date.now();

    // 更新音频级别引用
    audioLevelRef.current = level;

    // 更新UI显示的音频级别（限制更新频率）
    if (now - lastAudioLogTime.current > 100) { // 每100ms更新一次UI
      setAudioLevel(level);
      lastAudioLogTime.current = now;
    }

    // 只有当音频级别超过阈值时才记录到控制台
    if (level > 0.01) { // 调整这个阈值来控制敏感度
      // 限制日志频率，每1000ms最多记录一次
      if (now - lastAudioLogTime.current > 1000) {
        console.log(`🎤 音频输入检测 - 设备: ${deviceLabel}, 音频级别: ${(level * 100).toFixed(1)}%`);
      }
    }
  };

  // 强制关闭所有麦克风设备
  const forceCloseAllMicrophones = async () => {
    // 避免重复调用
    if ((window as any).__isClosingMicrophones) {
      console.log('麦克风关闭操作正在进行中，跳过重复调用');
      return;
    }

    (window as any).__isClosingMicrophones = true;
    console.log('强制关闭所有麦克风设备...');

    try {
      // 停止当前的录音流（只有在录音时才调用）
      if (isRecording) {
        stopRecording();
      }

      // 清理所有可能的全局音频流
      if (typeof window !== 'undefined') {
        const streams = (window as any).__activeMediaStreams || [];
        if (streams.length > 0) {
          console.log(`发现 ${streams.length} 个活跃的音频流，正在关闭...`);
          streams.forEach((stream: MediaStream, index: number) => {
            if (stream && typeof stream.getTracks === 'function') {
              stream.getTracks().forEach(track => {
                if (track.kind === 'audio' && track.readyState === 'live') {
                  track.stop();
                  console.log(`停止音频流 ${index + 1}:`, track.label);
                }
              });
            }
          });

          // 清空全局流数组
          (window as any).__activeMediaStreams = [];
        } else {
          console.log('没有发现活跃的音频流');
        }
      }

      console.log('所有麦克风设备已关闭');
    } catch (error) {
      console.error('关闭麦克风设备时出错:', error);
    } finally {
      (window as any).__isClosingMicrophones = false;
    }
  };

  // 获取音频设备列表（安全模式，最小化麦克风激活）
  const getAudioDevices = async () => {
    try {
      console.log('获取音频设备列表...');

      // 首先尝试不请求权限获取设备列表
      let devices = await navigator.mediaDevices.enumerateDevices();
      let audioInputDevices = devices.filter(device => device.kind === 'audioinput');

      // 检查是否已经有权限（通过检查是否有设备标签）
      const hasLabels = audioInputDevices.some(device => device.label);

      if (!hasLabels && audioInputDevices.length > 0) {
        console.log('设备列表无标签，检查权限状态...');

        // 检查权限状态
        if (navigator.permissions) {
          try {
            const permission = await navigator.permissions.query({ name: 'microphone' as PermissionName });
            console.log('麦克风权限状态:', permission.state);

            if (permission.state === 'granted') {
              // 如果已有权限但没有标签，重新获取设备列表
              devices = await navigator.mediaDevices.enumerateDevices();
              audioInputDevices = devices.filter(device => device.kind === 'audioinput');
            }
          } catch (error) {
            console.log('无法查询权限状态，将使用默认设备名称');
          }
        }
      }

      console.log('发现音频输入设备:', audioInputDevices.map(d => ({
        deviceId: d.deviceId,
        label: d.label || `麦克风 ${d.deviceId.slice(0, 8)}...`
      })));

      setAudioDevices(audioInputDevices);

      // 如果还没有选择设备，选择第一个
      if (!selectedDeviceId && audioInputDevices.length > 0) {
        setSelectedDeviceId(audioInputDevices[0].deviceId);
      }

      return audioInputDevices;
    } catch (error) {
      console.error('获取音频设备失败:', error);
      return [];
    }
  };

  // 连接到语音API
  const connectToVoiceAPI = async () => {
    setIsLoading(true);
    try {
      // 连接到后端API
      const response = await axios.post(`${apiBaseUrl}/api/voice-chat/connect`);
      
      if (response.data.success) {
        const { sessionId } = response.data;
        setSessionId(sessionId);
        
        // 建立WebSocket连接
        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        const host = window.location.hostname;
        const port = '5000'; // 使用服务器的实际端口
        const wsUrl = `${protocol}//${host}:${port}/api/voice-chat/ws/${sessionId}`;
        const ws = new WebSocket(wsUrl);
        
        ws.onopen = async () => {
          console.log('WebSocket连接已建立');
          setIsConnected(true);
          wsRef.current = ws;

          // 启动Python进程
          axios.post(`${apiBaseUrl}/api/voice-chat/start/${sessionId}`);

          // 获取音频设备列表
          await getAudioDevices();

          // 添加初始消息
          setMessages([{ text: t('voiceChat.welcome', '你好，来语音聊天吧！你可以说'), isUser: false }]);
        };
        
        ws.onmessage = (event) => {
          const data = JSON.parse(event.data);
          
          if (data.type === 'output') {
            // 处理Python输出
            handlePythonOutput(data.data);
          } else if (data.type === 'error') {
            console.error('Python错误:', data.data);
          } else if (data.type === 'exit') {
            console.log('Python进程已退出:', data.data);
            disconnectFromVoiceAPI();
          }
        };
        
        ws.onerror = (error) => {
          console.error('WebSocket错误:', error);
          setIsLoading(false);
        };
        
        ws.onclose = () => {
          console.log('WebSocket连接已关闭');
          wsRef.current = null;
          setIsConnected(false);
        };
      } else {
        console.log('连接失败:', response.data.message);
        setIsLoading(false);
      }
    } catch (error) {
      console.log('连接错误:', error);
      setIsLoading(false);
    }
  };

  // 处理Python输出
  const handlePythonOutput = (output: string) => {
    // 检查输出中是否包含AI的回复
    if (output.includes('服务器响应:')) {
      const match = output.match(/content": "(.+?)"/);
      if (match && match[1]) {
        const aiMessage = match[1];
        // setCurrentMessage(aiMessage);
        setMessages(prev => [...prev, { text: aiMessage, isUser: false }]);
        setIsProcessing(false);
      }
    }
  };

  // 断开连接
  const disconnectFromVoiceAPI = async () => {
    console.log('开始断开语音API连接...');

    try {
      // 首先停止录音，确保所有音频资源被释放
      stopRecording();

      // 强制关闭所有麦克风设备
      await forceCloseAllMicrophones();

      if (sessionId) {
        await axios.post(`${apiBaseUrl}/api/voice-chat/disconnect/${sessionId}`);
      }

      // 关闭WebSocket
      if (wsRef.current) {
        wsRef.current.close();
        wsRef.current = null;
      }

      setIsConnected(false);
      setSessionId(null);
      setIsProcessing(false);

      console.log('语音API连接已断开，所有麦克风已关闭');
    } catch (error) {
      console.log('断开连接错误:', error);
    }
  };

  // 开始录音
  const startRecording = async () => {
    try {
      // 确保当前没有在录音
      if (isRecording) {
        console.log('已经在录音中，忽略重复请求');
        return;
      }

      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
        alert('您的浏览器不支持录音功能');
        return;
      }

      // 确保没有其他录音流在运行
      if (mediaStreamRef.current) {
        console.log('检测到现有录音流，先停止...');
        stopRecording();
        await new Promise(resolve => setTimeout(resolve, 100));
      }

      console.log('开始请求麦克风权限...');
      console.log('选择的设备ID:', selectedDeviceId);

      // 构建音频约束
      const audioConstraints: MediaTrackConstraints = {
        echoCancellation: true,
        noiseSuppression: true,
        autoGainControl: true
      };

      // 如果选择了特定设备，添加设备ID约束
      if (selectedDeviceId) {
        audioConstraints.deviceId = { exact: selectedDeviceId };
      }

      // 获取麦克风权限
      const stream = await navigator.mediaDevices.getUserMedia({
        audio: audioConstraints
      });
      mediaStreamRef.current = stream;

      // 将流添加到全局跟踪数组
      if (typeof window !== 'undefined') {
        if (!(window as any).__activeMediaStreams) {
          (window as any).__activeMediaStreams = [];
        }
        (window as any).__activeMediaStreams.push(stream);
      }

      // 记录实际使用的设备
      const audioTrack = stream.getAudioTracks()[0];
      let deviceLabel = '未知设备';

      if (audioTrack) {
        deviceLabel = audioTrack.label || `设备 ${selectedDeviceId?.slice(0, 8)}...`;
        console.log('🎤 实际使用的音频设备:', deviceLabel);
        console.log('设备设置:', audioTrack.getSettings());

        // 保存当前设备标签
        setCurrentDeviceLabel(deviceLabel);

        // 添加轨道结束监听器
        audioTrack.addEventListener('ended', () => {
          console.log('音频轨道已结束:', deviceLabel);
          setIsRecording(false);
          setCurrentDeviceLabel('');
        });
      }

      console.log('麦克风权限获取成功，创建音频上下文...');

      // 创建音频上下文
      const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
      audioContextRef.current = audioContext;

      // 创建麦克风输入源
      const microphone = audioContext.createMediaStreamSource(stream);
      microphoneRef.current = microphone;

      // 创建音频分析器用于监控音频级别
      const analyser = audioContext.createAnalyser();
      analyser.fftSize = 256;
      analyser.smoothingTimeConstant = 0.8;
      analyserRef.current = analyser;

      // 创建处理节点
      const processor = audioContext.createScriptProcessor(4096, 1, 1);
      processorRef.current = processor;

      // 处理音频数据
      processor.onaudioprocess = (e) => {
        // 严格检查录音状态和WebSocket连接
        if (!isRecording || !wsRef.current || wsRef.current.readyState !== WebSocket.OPEN) {
          return;
        }

        const inputData = e.inputBuffer.getChannelData(0);

        // 计算音频级别并记录设备信息
        const audioLevel = calculateAudioLevel(inputData);
        logAudioInput(audioLevel, deviceLabel);

        const output = convertFloat32ToInt16(inputData);

        // 发送音频数据到WebSocket
        wsRef.current.send(JSON.stringify({
          type: 'audio',
          data: arrayBufferToBase64(output)
        }));
      };

      // 连接音频节点：麦克风 -> 分析器 -> 处理器 -> 输出
      microphone.connect(analyser);
      analyser.connect(processor);
      processor.connect(audioContext.destination);

      // 发送开始录音信号到后端
      if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {
        wsRef.current.send(JSON.stringify({
          type: 'start_recording',
          deviceId: selectedDeviceId,
          deviceLabel: deviceLabel
        }));
        console.log('已发送开始录音信号到后端');
      }

      // 设置录音状态
      setIsRecording(true);
      setIsProcessing(true);

      console.log('录音已开始');

      // 添加用户消息
      setMessages(prev => [...prev, { text: '正在录音...', isUser: true }]);
    } catch (error) {
      console.error('启动录音失败:', error);
      if (error instanceof DOMException) {
        if (error.name === 'NotAllowedError') {
          alert('麦克风权限被拒绝，请允许访问麦克风后重试');
        } else if (error.name === 'NotFoundError') {
          alert('未找到麦克风设备');
        } else {
          alert('无法访问麦克风: ' + error.message);
        }
      } else {
        alert('无法访问麦克风，请检查设备和权限');
      }
      // 确保状态重置
      setIsRecording(false);
      setIsProcessing(false);
    }
  };

  // 停止录音
  const stopRecording = () => {
    console.log('停止录音...');

    // 发送停止录音信号到后端
    if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {
      wsRef.current.send(JSON.stringify({
        type: 'stop_recording'
      }));
      console.log('已发送停止录音信号到后端');
    }

    // 先设置状态为false，防止继续处理音频
    setIsRecording(false);

    // 断开并清理音频处理节点
    if (processorRef.current) {
      try {
        processorRef.current.disconnect();
        processorRef.current.onaudioprocess = null;
      } catch (error) {
        console.error('断开处理器失败:', error);
      }
      processorRef.current = null;
    }

    // 断开并清理音频分析器
    if (analyserRef.current) {
      try {
        analyserRef.current.disconnect();
      } catch (error) {
        console.error('断开分析器失败:', error);
      }
      analyserRef.current = null;
    }

    // 断开并清理麦克风输入源
    if (microphoneRef.current) {
      try {
        microphoneRef.current.disconnect();
      } catch (error) {
        console.error('断开麦克风失败:', error);
      }
      microphoneRef.current = null;
    }

    // 停止并清理媒体流
    if (mediaStreamRef.current) {
      try {
        mediaStreamRef.current.getTracks().forEach(track => {
          track.stop();
          console.log('音频轨道已停止:', track.label);
        });

        // 从全局跟踪数组中移除
        if (typeof window !== 'undefined' && (window as any).__activeMediaStreams) {
          const streams = (window as any).__activeMediaStreams;
          const index = streams.indexOf(mediaStreamRef.current);
          if (index > -1) {
            streams.splice(index, 1);
            console.log('已从全局跟踪数组中移除流');
          }
        }
      } catch (error) {
        console.error('停止媒体流失败:', error);
      }
      mediaStreamRef.current = null;
    }

    // 关闭并清理音频上下文
    if (audioContextRef.current) {
      try {
        if (audioContextRef.current.state !== 'closed') {
          audioContextRef.current.close().catch(console.error);
        }
      } catch (error) {
        console.error('关闭音频上下文失败:', error);
      }
      audioContextRef.current = null;
    }

    // 清理设备标签和音频级别
    setCurrentDeviceLabel('');
    setAudioLevel(0);
    audioLevelRef.current = 0;

    console.log('录音已停止，所有资源已清理');
  };

  // 转换音频格式
  const convertFloat32ToInt16 = (buffer: Float32Array) => {
    const l = buffer.length;
    const buf = new Int16Array(l);
    
    for (let i = 0; i < l; i++) {
      buf[i] = Math.min(1, Math.max(-1, buffer[i])) * 0x7FFF;
    }
    
    return buf.buffer;
  };

  // ArrayBuffer转Base64
  const arrayBufferToBase64 = (buffer: ArrayBuffer) => {
    let binary = '';
    const bytes = new Uint8Array(buffer);
    const len = bytes.byteLength;
    
    for (let i = 0; i < len; i++) {
      binary += String.fromCharCode(bytes[i]);
    }
    
    return window.btoa(binary);
  };

  // 处理设备选择变化
  const handleDeviceChange = (deviceId: string) => {
    console.log('切换音频设备:', deviceId);

    // 如果正在录音，先停止录音
    if (isRecording) {
      stopRecording();
      // 给一点时间让资源清理完成，然后用新设备重新开始录音
      setTimeout(() => {
        setSelectedDeviceId(deviceId);
        startRecording();
      }, 100);
    } else {
      setSelectedDeviceId(deviceId);
    }
  };

  // 处理录音按钮点击
  const handleRecordButtonClick = () => {
    console.log('录音按钮被点击，当前录音状态:', isRecording);

    if (!isConnected) {
      alert('请先连接到语音服务');
      return;
    }

    if (isRecording) {
      console.log('停止录音');
      stopRecording();
    } else {
      console.log('开始录音');
      startRecording();
    }
  };

  // 滚动到底部
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // 组件初始化时确保录音状态为关闭
  useEffect(() => {
    // 确保初始状态正确
    setIsRecording(false);
    setIsProcessing(false);

    // 监听设备变化
    const handleDeviceChange = () => {
      console.log('检测到音频设备变化，重新获取设备列表');
      if (isConnected) {
        getAudioDevices();
      }
    };

    if (navigator.mediaDevices && navigator.mediaDevices.addEventListener) {
      navigator.mediaDevices.addEventListener('devicechange', handleDeviceChange);
    }

    // 清理可能存在的音频资源
    return () => {
      if (navigator.mediaDevices && navigator.mediaDevices.removeEventListener) {
        navigator.mediaDevices.removeEventListener('devicechange', handleDeviceChange);
      }
    };
  }, [isConnected]);

  // 页面加载时的一次性初始化
  useEffect(() => {
    // 页面加载时强制关闭所有麦克风（只执行一次）
    forceCloseAllMicrophones();

    return () => {
      // 组件卸载时清理
      forceCloseAllMicrophones();
    };
  }, []);

  // 监听页面可见性变化
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.hidden) {
        console.log('页面隐藏，停止录音并关闭麦克风');
        if (isRecording) {
          stopRecording();
        }
        forceCloseAllMicrophones();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    // 监听页面卸载
    const handleBeforeUnload = () => {
      console.log('页面即将卸载，关闭所有麦克风');
      forceCloseAllMicrophones();
    };

    window.addEventListener('beforeunload', handleBeforeUnload);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, [isRecording]);

  // 组件卸载时断开连接
  useEffect(() => {
    return () => {
      if (isConnected) {
        disconnectFromVoiceAPI();
      }
    };
  }, [isConnected]);

  return (
    <div className={`voice-chat-container ${theme === 'dark' ? 'dark' : ''}`}>
      <Helmet>
        <title>{t('voiceChat.pageTitle', 'AI语音聊天')}</title>
        <meta name="description" content={t('voiceChat.pageDescription', '使用实时语音与AI助手对话')} />
      </Helmet>

      <LandingBackground />

      {/* 麦克风状态指示器 */}
      {isRecording && (
        <div className="voice-chat-mic-indicator">
          <div className="voice-chat-mic-indicator-icon">
            <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M12 14c1.66 0 3-1.34 3-3V5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3z" fill="currentColor"/>
              <path d="M17 11c0 2.76-2.24 5-5 5s-5-2.24-5-5H5c0 3.53 2.61 6.43 6 6.92V21h2v-3.08c3.39-.49 6-3.39 6-6.92h-2z" fill="currentColor"/>
            </svg>
          </div>
          <span className="voice-chat-mic-indicator-text">
            麦克风已激活 - {audioDevices.find(d => d.deviceId === selectedDeviceId)?.label || '未知设备'}
          </span>
        </div>
      )}

      {/* 页面标题 */}
      <div className="voice-chat-title-section text-center mb-4 sm:mb-6 mt-6 sm:mt-8 md:mt-10">
        <h1 className="main-title mb-2 sm:mb-3 dark:text-white text-gray-900">{t('voiceChat.pageTitle', 'AI语音聊天')}</h1>
        <p className="text-base sm:text-lg dark:text-purple-300 text-purple-600 italic">{t('voiceChat.pageDescription', '使用实时语音与AI助手对话，获得即时回应和帮助')}</p>
      </div>

      {!isConnected ? (
        <div className="voice-chat-start">
          <div className="voice-chat-start-content">
            <div className="voice-chat-icon">
              <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 15.5c2.21 0 4-1.79 4-4V6c0-2.21-1.79-4-4-4S8 3.79 8 6v5.5c0 2.21 1.79 4 4 4z" fill="currentColor"/>
                <path d="M19 10v1.5c0 3.87-3.13 7-7 7s-7-3.13-7-7V10H3v1.5c0 4.55 3.4 8.3 7.8 8.9v1.6h-3.3v2h9v-2h-3.3v-1.6c4.4-.6 7.8-4.35 7.8-8.9V10h-2z" fill="currentColor"/>
              </svg>
            </div>
            <h2 className="voice-chat-start-title">{t('voiceChat.startVoiceChat', '开始语音对话')}</h2>
            <p className="voice-chat-start-description">
              {t('voiceChat.startDescription', '点击下方按钮开始与AI助手进行实时语音对话，获得即时回应和帮助。')}
            </p>
            <button 
              className="voice-chat-button"
              onClick={connectToVoiceAPI}
              disabled={isLoading}
            >
              <div className="voice-chat-button-icon">
                <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M12 15.5c2.21 0 4-1.79 4-4V6c0-2.21-1.79-4-4-4S8 3.79 8 6v5.5c0 2.21 1.79 4 4 4z" fill="currentColor"/>
                  <path d="M19 10v1.5c0 3.87-3.13 7-7 7s-7-3.13-7-7V10H3v1.5c0 4.55 3.4 8.3 7.8 8.9v1.6h-3.3v2h9v-2h-3.3v-1.6c4.4-.6 7.8-4.35 7.8-8.9V10h-2z" fill="currentColor"/>
                </svg>
              </div>
              {isLoading ? t('voiceChat.connecting', '正在连接...') : t('voiceChat.startVoiceChat', '开始语音对话')}
            </button>
          </div>
        </div>
      ) : (
        <div className="voice-chat-session">
          <div className="voice-chat-header">
            <div className="voice-chat-header-left">
              <div className="voice-chat-header-avatar">
                <CdnLazyImage src="/images/readers/Vivi.webp" alt="AI Assistant" />
              </div>
              <h1>
                {t('voiceChat.title', 'Vivi')}
                <span className="voice-chat-status-indicator"></span>
              </h1>
            </div>
            <button className="voice-chat-close" onClick={disconnectFromVoiceAPI}>
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <line x1="18" y1="6" x2="6" y2="18"></line>
                <line x1="6" y1="6" x2="18" y2="18"></line>
              </svg>
              {t('voiceChat.disconnect', '断开连接')}
            </button>
          </div>
          
          <div className="voice-chat-content">
            <div className="voice-chat-sidebar">
              <div className="voice-chat-avatar-container">
                <div className="voice-chat-avatar" ref={avatarRef}>
                  <CdnLazyImage src="/images/readers/Vivi.webp" alt="AI Assistant" />
                </div>
                <h3 className="voice-chat-assistant-name">{t('voiceChat.title', 'Vivi')}</h3>
                <p className="voice-chat-assistant-title">{t('voiceChat.assistantTitle', 'AI语音助手')}</p>
                <div className={`voice-chat-status ${isRecording ? 'recording' : ''}`}>
                  <div className="voice-chat-status-dot"></div>
                  {isRecording
                    ? t('voiceChat.recording', '正在录音...')
                    : isProcessing
                      ? t('voiceChat.speaking', '正在说话...')
                      : t('voiceChat.listening', '正在听...')}
                </div>
                {isRecording && selectedDeviceId && (
                  <div className="voice-chat-current-device">
                    <span className="voice-chat-current-device-label">当前麦克风:</span>
                    <span className="voice-chat-current-device-name">
                      {currentDeviceLabel || audioDevices.find(d => d.deviceId === selectedDeviceId)?.label || '未知设备'}
                    </span>
                    <div className="voice-chat-audio-level">
                      <span className="voice-chat-audio-level-label">音频级别:</span>
                      <div className="voice-chat-audio-level-bar">
                        <div
                          className="voice-chat-audio-level-fill"
                          style={{ width: `${audioLevel * 100}%` }}
                        ></div>
                      </div>
                      <span className="voice-chat-audio-level-value">
                        {(audioLevel * 100).toFixed(0)}%
                      </span>
                    </div>
                  </div>
                )}
              </div>

              <div className="voice-chat-sidebar-divider"></div>
              
              <div className="voice-chat-sidebar-section">
                <h4 className="voice-chat-sidebar-title">{t('voiceChat.microphoneSettings', '麦克风设置')}</h4>
                <div className="voice-chat-sidebar-content">
                  <div className="voice-chat-device-selector">
                    <label htmlFor="microphone-select" className="voice-chat-device-label">
                      {t('voiceChat.selectMicrophone', '选择麦克风')}:
                    </label>
                    <select
                      id="microphone-select"
                      className="voice-chat-device-select"
                      value={selectedDeviceId}
                      onChange={(e) => handleDeviceChange(e.target.value)}
                      disabled={isRecording}
                    >
                      {audioDevices.map((device) => (
                        <option key={device.deviceId} value={device.deviceId}>
                          {device.label || `麦克风 ${device.deviceId.slice(0, 8)}...`}
                        </option>
                      ))}
                    </select>
                    {audioDevices.length > 1 && (
                      <div className="voice-chat-device-info">
                        {t('voiceChat.multipleDevicesDetected', `检测到 ${audioDevices.length} 个麦克风设备`)}
                      </div>
                    )}
                    <button
                      className="voice-chat-refresh-devices"
                      onClick={getAudioDevices}
                      disabled={isRecording}
                      title={t('voiceChat.refreshDevices', '刷新设备列表')}
                    >
                      <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                      </svg>
                      {t('voiceChat.refresh', '刷新')}
                    </button>
                  </div>
                </div>
              </div>

              <div className="voice-chat-sidebar-section">
                <h4 className="voice-chat-sidebar-title">{t('voiceChat.instructions', '使用说明')}</h4>
                <div className="voice-chat-sidebar-content">
                  {t('voiceChat.instructionsContent', '点击中间的录音按钮开始说话，再次点击结束录音。AI助手将会分析您的语音并给出回应。')}
                </div>
              </div>

              <div className="voice-chat-sidebar-section">
                <h4 className="voice-chat-sidebar-title">{t('voiceChat.tips', '小贴士')}</h4>
                <div className="voice-chat-sidebar-content">
                  {t('voiceChat.tipsContent', '请在安静的环境中使用，说话清晰，语速适中，以获得最佳体验。')}
                </div>
              </div>
            </div>
            
            <div className="voice-chat-main">
              <div className="voice-chat-messages">
                {messages.map((msg, index) => (
                  <div key={index} className={`voice-chat-message ${msg.isUser ? 'user' : 'assistant'}`}>
                    <div className="voice-chat-message-header">
                      <div className="voice-chat-message-avatar">
                        <CdnLazyImage 
                          src={msg.isUser ? "/images/readers/user-avatar.webp" : "/images/readers/Vivi.webp"} 
                          alt={msg.isUser ? "User" : "AI Assistant"} 
                        />
                      </div>
                      <div className="voice-chat-message-name">
                        {msg.isUser ? t('voiceChat.you', '你') : t('voiceChat.title', 'Vivi')}
                      </div>
                    </div>
                    <div className="voice-chat-message-bubble">
                      {msg.text}
                    </div>
                  </div>
                ))}
                <div ref={messagesEndRef} />
              </div>
              
              <div className="voice-chat-controls">
                <div className="voice-chat-timer">
                  {isRecording ? t('voiceChat.recording', '正在录音...') : t('voiceChat.clickToSpeak', '点击下方按钮开始说话')}
                </div>
                <div className="voice-chat-buttons">
                  <button className="voice-chat-control-button">
                    <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M12 14c1.66 0 3-1.34 3-3V5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3z" fill="currentColor"/>
                      <path d="M17 11c0 2.76-2.24 5-5 5s-5-2.24-5-5H5c0 3.53 2.61 6.43 6 6.92V21h2v-3.08c3.39-.49 6-3.39 6-6.92h-2z" fill="currentColor"/>
                    </svg>
                  </button>
                  <button
                    className={`voice-chat-control-button record ${isRecording ? 'active' : ''}`}
                    onClick={handleRecordButtonClick}
                    disabled={!isConnected}
                    title={isRecording ? '点击停止录音' : '点击开始录音'}
                  >
                    {isRecording ? (
                      <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <rect x="6" y="6" width="12" height="12" fill="currentColor"/>
                      </svg>
                    ) : (
                      <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <circle cx="12" cy="12" r="8" fill="currentColor"/>
                      </svg>
                    )}
                  </button>
                  <button className="voice-chat-control-button">
                    <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M3 9v6h4l5 5V4L7 9H3zm13.5 3c0-1.77-1.02-3.29-2.5-4.03v8.05c1.48-.73 2.5-2.25 2.5-4.02zM14 3.23v2.06c2.89.86 5 3.54 5 6.71s-2.11 5.85-5 6.71v2.06c4.01-.91 7-4.49 7-8.77s-2.99-7.86-7-8.77z" fill="currentColor"/>
                    </svg>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default VoiceChat; 