{"app": {"name": "TarotQA"}, "auth": {"validation": {"invalidEmail": "Please enter a valid email address", "invalidPassword": "Password can only contain letters, numbers, and underscores", "emailExists": "This email has already been registered", "usernameExists": "This username is already in use", "emailNotExists": "This email is not registered", "invalidCredentials": "Incorrect email or password", "invalidCode": "Invalid or expired verification code", "invalidToken": "Reset link has expired, please request a new one", "userNotExists": "User does not exist", "unauthorized": "You are not authorized to update other users' information", "emailNotReceivable": "Email address does not exist or cannot receive emails, please check and try again", "provideEmail": "Please provide an email address", "userIdEmailMismatch": "User ID and email do not match"}, "success": {"codeSent": "Verification code has been sent to your email", "emailVerified": "Email verification successful", "passwordReset": "Password reset successful", "resetLinkSent": "Password reset link has been sent to your email", "resetCodeSent": "Password reset code has been sent to your email", "codeVerified": "Verification code verified successfully", "testEmailSent": "Test email has been sent"}, "error": {"registration": "Registration failed, please try again later", "verification": "Verification failed, please try again later", "login": "<PERSON><PERSON> failed, please try again later", "getUserInfo": "Failed to get user information", "sendResetEmail": "Failed to send password reset email, please try again later", "resetPassword": "Failed to reset password, please try again later", "sendCode": "Failed to send verification code, please try again later", "updateUserInfo": "Failed to update user information", "testEmailFailed": "Failed to send test email", "authFailed": "Authentication failed"}, "registration": {"expiredInfo": "Registration information has expired, please register again", "expiredCode": "Verification code has expired, please register again", "expiredVerification": "Verification code has expired, please register again", "incorrectCode": "Incorrect verification code, please try again"}, "email": {"registerSubject": "Verify Your Email", "registerTitle": "Welcome to TarotQA", "registerCodeMessage": "Your verification code is:", "registerCodeExpiry": "This code is valid for 30 minutes. If you did not request this, please ignore this email.", "resetPasswordSubject": "Reset Your Password", "resetPasswordTitle": "Password Reset", "resetPasswordGreeting": "Hello, we received a request to reset your password.", "resetPasswordInstruction": "Please click the link below to reset your password:", "resetPasswordExpiry": "This link is valid for 1 hour. If you did not request a password reset, please ignore this email.", "resetCodeSubject": "Password Reset Code", "resetCodeMessage": "Your verification code is: {{code}}", "resetCodeExpiry": "This code is valid for 30 minutes. If you did not request this, please ignore this email.", "testSubject": "Test Email", "testTitle": "Test Email", "testMessage": "This is a test email to verify that the email service is working properly.", "timestamp": "Sent at: {{time}}", "footer": "This email was sent automatically. Please do not reply."}}, "session": {"userNotExists": "User does not exist", "noRemainingReads": "You have used all your free readings, please upgrade to VIP to continue"}, "stats": {"updateFailed": "Failed to update user statistics", "getFailed": "Failed to get user statistics", "getApiFailed": "Failed to get API statistics"}, "payment": {"invalidAmount": "Invalid payment amount", "incompleteInfo": "Incomplete product information", "invalidOrder": "Invalid order number", "success": "Success", "signatureVerificationFailed": "Signature verification failed", "invalidTradeStatus": "Invalid trade status", "invalidAmountFormat": "Invalid amount format", "orderProcessed": "Order has been processed"}, "middleware": {"unauthorized": "Unauthorized access", "invalidToken": "Invalid token"}, "reading": {"cards": {"major": {"0": "The Fool", "1": "The Magician", "2": "The High Priestess", "3": "The Empress", "4": "The Emperor", "5": "The Hierophant", "6": "The Lovers", "7": "The Chariot", "8": "Strength", "9": "The Hermit", "10": "Wheel of Fortune", "11": "Justice", "12": "The Hanged Man", "13": "Death", "14": "Temperance", "15": "The Devil", "16": "The Tower", "17": "The Star", "18": "The Moon", "19": "The Sun", "20": "Judgement", "21": "The World"}, "wands": {"ace": "Ace of Wands", "2": "Two of Wands", "3": "Three of Wands", "4": "Four of Wands", "5": "Five of Wands", "6": "Six of Wands", "7": "Seven of Wands", "8": "Eight of Wands", "9": "Nine of Wands", "10": "Ten of Wands", "page": "Page of Wands", "knight": "Knight of Wands", "queen": "Queen of Wands", "king": "King of Wands"}, "cups": {"ace": "Ace of Cups", "2": "Two of Cups", "3": "Three of Cups", "4": "Four of Cups", "5": "Five of Cups", "6": "Six of Cups", "7": "Seven of Cups", "8": "Eight of Cups", "9": "Nine of Cups", "10": "Ten of Cups", "page": "Page of Cups", "knight": "Knight of Cups", "queen": "Queen of Cups", "king": "King of Cups"}, "swords": {"ace": "Ace of Swords", "2": "Two of Swords", "3": "Three of Swords", "4": "Four of Swords", "5": "Five of Swords", "6": "Six of Swords", "7": "Seven of Swords", "8": "Eight of Swords", "9": "Nine of Swords", "10": "Ten of Swords", "page": "Page of Swords", "knight": "Knight of Swords", "queen": "Queen of Swords", "king": "King of Swords"}, "pentacles": {"ace": "Ace of Pentacles", "2": "Two of Pentacles", "3": "Three of Pentacles", "4": "Four of Pentacles", "5": "Five of Pentacles", "6": "Six of Pentacles", "7": "Seven of Pentacles", "8": "Eight of Pentacles", "9": "Nine of Pentacles", "10": "Ten of Pentacles", "page": "Page of Pentacles", "knight": "Knight of Pentacles", "queen": "Queen of Pentacles", "king": "King of Pentacles"}}}}